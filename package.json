{"name": "fast-ocr", "version": "1.0.0", "description": "A fast OCR application built with Electron and TypeScript", "main": "dist/main.js", "scripts": {"build": "tsc", "start": "npm run build && electron .", "dev": "tsc && electron . --dev", "clean": "<PERSON><PERSON><PERSON> dist"}, "keywords": ["electron", "typescript", "ocr"], "author": "", "license": "MIT", "devDependencies": {"@types/node": "^20.0.0", "electron": "^27.0.0", "rimraf": "^5.0.0", "typescript": "^5.0.0"}, "dependencies": {}}