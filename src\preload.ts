import { contextBridge, ipcRenderer } from 'electron';

// 暴露受保护的方法，允许渲染进程使用 ipcRenderer，而不暴露整个对象
contextBridge.exposeInMainWorld('electronAPI', {
  // 示例：发送消息到主进程
  sendMessage: (message: string) => ipcRenderer.invoke('send-message', message),
  
  // 示例：监听来自主进程的消息
  onMessage: (callback: (message: string) => void) => {
    ipcRenderer.on('message-from-main', (_event, message) => callback(message));
  },

  // 移除监听器
  removeAllListeners: (channel: string) => {
    ipcRenderer.removeAllListeners(channel);
  }
});

// 类型声明，用于 TypeScript 支持
declare global {
  interface Window {
    electronAPI: {
      sendMessage: (message: string) => Promise<any>;
      onMessage: (callback: (message: string) => void) => void;
      removeAllListeners: (channel: string) => void;
    };
  }
}
