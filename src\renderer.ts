// 渲染进程的主要逻辑文件

// 等待 DOM 加载完成
document.addEventListener('DOMContentLoaded', () => {
  console.log('渲染进程已启动');

  // 检查 electronAPI 是否可用
  if (window.electronAPI) {
    console.log('Electron API 已成功暴露到渲染进程');
    
    // 示例：设置按钮点击事件
    const sendButton = document.getElementById('send-button') as HTMLButtonElement;
    const messageInput = document.getElementById('message-input') as HTMLInputElement;
    const messagesDiv = document.getElementById('messages') as HTMLDivElement;

    if (sendButton && messageInput && messagesDiv) {
      sendButton.addEventListener('click', async () => {
        const message = messageInput.value.trim();
        if (message) {
          try {
            // 发送消息到主进程
            await window.electronAPI.sendMessage(message);
            
            // 在界面上显示发送的消息
            const messageElement = document.createElement('div');
            messageElement.className = 'message sent';
            messageElement.textContent = `发送: ${message}`;
            messagesDiv.appendChild(messageElement);
            
            // 清空输入框
            messageInput.value = '';
            
            // 滚动到底部
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
          } catch (error) {
            console.error('发送消息失败:', error);
          }
        }
      });

      // 监听回车键发送消息
      messageInput.addEventListener('keypress', (event) => {
        if (event.key === 'Enter') {
          sendButton.click();
        }
      });
    }

    // 监听来自主进程的消息
    window.electronAPI.onMessage((message: string) => {
      console.log('收到来自主进程的消息:', message);
      
      if (messagesDiv) {
        const messageElement = document.createElement('div');
        messageElement.className = 'message received';
        messageElement.textContent = `接收: ${message}`;
        messagesDiv.appendChild(messageElement);
        messagesDiv.scrollTop = messagesDiv.scrollHeight;
      }
    });
  } else {
    console.error('Electron API 未能正确暴露到渲染进程');
  }
});

// 在窗口卸载时清理监听器
window.addEventListener('beforeunload', () => {
  if (window.electronAPI) {
    window.electronAPI.removeAllListeners('message-from-main');
  }
});
