/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
}

.container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 头部样式 */
header {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

header h1 {
    font-size: 2.5em;
    margin-bottom: 10px;
    font-weight: 300;
}

header p {
    font-size: 1.1em;
    opacity: 0.9;
}

/* 主要内容区域 */
main {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.demo-section {
    background: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.demo-section h2 {
    margin-bottom: 20px;
    color: #555;
    font-weight: 400;
}

/* 输入组样式 */
.input-group {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

#message-input {
    flex: 1;
    padding: 12px 16px;
    border: 2px solid #e1e5e9;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

#message-input:focus {
    outline: none;
    border-color: #667eea;
}

#send-button {
    padding: 12px 24px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

#send-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);
}

#send-button:active {
    transform: translateY(0);
}

/* 消息容器样式 */
.messages-container {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #e1e5e9;
    border-radius: 6px;
    padding: 15px;
    background-color: #fafafa;
}

.message {
    margin-bottom: 10px;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 14px;
}

.message.system {
    background-color: #e3f2fd;
    color: #1565c0;
    border-left: 4px solid #2196f3;
}

.message.sent {
    background-color: #e8f5e8;
    color: #2e7d32;
    border-left: 4px solid #4caf50;
}

.message.received {
    background-color: #fff3e0;
    color: #ef6c00;
    border-left: 4px solid #ff9800;
}

/* 底部样式 */
footer {
    text-align: center;
    padding: 20px;
    color: #666;
    font-size: 0.9em;
}

/* 滚动条样式 */
.messages-container::-webkit-scrollbar {
    width: 6px;
}

.messages-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.messages-container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.messages-container::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    header h1 {
        font-size: 2em;
    }
    
    .demo-section {
        padding: 20px;
    }
    
    .input-group {
        flex-direction: column;
    }
    
    #send-button {
        align-self: flex-start;
    }
}
