<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline';">
    <title>Fast OCR</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>Fast OCR</h1>
            <p>基于 Electron 和 TypeScript 的 OCR 应用</p>
        </header>

        <main>
            <section class="demo-section">
                <h2>演示功能</h2>
                <div class="input-group">
                    <input type="text" id="message-input" placeholder="输入消息..." />
                    <button id="send-button">发送</button>
                </div>
                
                <div id="messages" class="messages-container">
                    <div class="message system">应用已启动，可以开始使用了！</div>
                </div>
            </section>
        </main>

        <footer>
            <p>Powered by Electron & TypeScript</p>
        </footer>
    </div>

    <!-- 加载编译后的渲染进程脚本 -->
    <script src="../dist/renderer.js"></script>
</body>
</html>
